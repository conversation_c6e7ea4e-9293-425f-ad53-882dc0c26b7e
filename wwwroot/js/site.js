document.addEventListener("DOMContentLoaded", () => {
  const heroBgContainer = document.getElementById(
    "hero-interactive-background"
  );
  if (heroBgContainer) {
    const canvas = document.createElement("canvas");
    heroBgContainer.appendChild(canvas);
    const ctx = canvas.getContext("2d");
    let width, height;
    let points = [];
    let mouse = { x: 0, y: 0 };
    const numPoints = window.innerWidth < 768 ? 30 : 85;
    const connectionDistance = window.innerWidth < 768 ? 98 : 180;
    const flowSpeed = 0.005;
    const pulseSpeed = 0.05;

    const resizeCanvas = () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
      createPoints();
    };

    class Point {
      constructor() {
        this.x = Math.random() * width;
        this.y = Math.random() * height;
        this.vx = (Math.random() - 0.5) * 0.5;
        this.vy = (Math.random() - 0.5) * 0.5;
        this.radius = 1 + Math.random() * 1.5;
        this.color =
          Math.random() > 0.5
            ? "var(--color-accent)"
            : "var(--color-secondary-accent)";
        this.alpha = Math.random() * 0.5 + 0.2;
      }

      update() {
        this.x += this.vx;
        this.y += this.vy;

        if (this.x < 0 || this.x > width) this.vx *= -1;
        if (this.y < 0 || this.y > height) this.vy *= -1;
      }

      draw() {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.globalAlpha = this.alpha;
        ctx.fill();
        ctx.globalAlpha = 1;
      }
    }

    const createPoints = () => {
      points = [];
      for (let i = 0; i < numPoints; i++) {
        points.push(new Point());
      }
    };

    const drawConnections = () => {
      for (let i = 0; i < points.length; i++) {
        for (let j = i + 1; j < points.length; j++) {
          const p1 = points[i];
          const p2 = points[j];
          const dist = Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2);

          if (dist < connectionDistance) {
            let alpha = 1 - dist / connectionDistance;

            const mouseDist = Math.sqrt(
              (p1.x - mouse.x) ** 2 + (p1.y - mouse.y) ** 2
            );
            const mouseAlpha = Math.max(0, 1 - mouseDist / 200);
            alpha = Math.max(alpha, mouseAlpha);

            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.strokeStyle = `rgba(211, 47, 47, ${alpha * 0.5})`;
            ctx.lineWidth = 0.5 + alpha * 1;
            ctx.stroke();
          }
        }
      }
    };

    const animateBackground = () => {
      requestAnimationFrame(animateBackground);
      ctx.clearRect(0, 0, width, height);

      points.forEach((p) => {
        p.update();
        p.draw();
      });
      drawConnections();
    };

    canvas.addEventListener("mousemove", (e) => {
      mouse.x = e.clientX;
      mouse.y = e.clientY;
    });

    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    animateBackground();
  }

  const parallaxContainers = document.querySelectorAll(
    "[data-parallax-container]"
  );
  const revealElements = document.querySelectorAll(".reveal-on-scroll");
  const navbar = document.getElementById("mainNavbar");
  const initialNavbarPaddingTop = parseFloat(
    getComputedStyle(navbar).paddingTop
  );
  const initialNavbarPaddingBottom = parseFloat(
    getComputedStyle(navbar).paddingBottom
  );

  // Parallax scroll state management
  let isScrolling = false;
  let scrollTimeout;
  let ticking = false;

  const updateParallax = () => {
    const scrollTop = window.pageYOffset;
    parallaxContainers.forEach((container) => {
      const elements = container.querySelectorAll("[data-parallax-element]");
      const containerTop = container.getBoundingClientRect().top + scrollTop;

      elements.forEach((element) => {
        const speed = parseFloat(element.dataset.parallaxSpeed);
        if (!isNaN(speed)) {
          const yPos = (containerTop - scrollTop) * speed;
          element.style.transform = `translateY(${yPos}px)`;
        }
      });
    });
  };

  const enableParallaxTransitions = () => {
    const parallaxElements = document.querySelectorAll(
      "[data-parallax-element]"
    );
    parallaxElements.forEach((element) => {
      element.classList.remove("scrolling");
    });
  };

  const disableParallaxTransitions = () => {
    const parallaxElements = document.querySelectorAll(
      "[data-parallax-element]"
    );
    parallaxElements.forEach((element) => {
      element.classList.add("scrolling");
    });
  };

  const handleScrollStart = () => {
    if (!isScrolling) {
      isScrolling = true;
      disableParallaxTransitions();
    }

    // Clear the timeout
    clearTimeout(scrollTimeout);

    // Set a timeout to detect when scrolling stops
    scrollTimeout = setTimeout(() => {
      isScrolling = false;
      enableParallaxTransitions();

      // Trigger a final parallax update with transitions enabled
      requestAnimationFrame(() => {
        updateParallax();
      });
    }, 150); // 150ms delay after scroll stops
  };

  // Enhanced scroll-triggered animations with Intersection Observer
  const initializeScrollAnimations = () => {
    // Fallback for browsers that don't support Intersection Observer
    if (!window.IntersectionObserver) {
      const checkRevealAnimations = () => {
        revealElements.forEach((element) => {
          const elementTop = element.getBoundingClientRect().top;
          const viewportHeight = window.innerHeight;

          // Trigger when element is 20-30% visible (changed from 75%)
          if (elementTop < viewportHeight * 0.8) {
            element.classList.add("active");
          }
        });
      };

      window.addEventListener("scroll", checkRevealAnimations);
      checkRevealAnimations(); // Check on page load
      return;
    }

    // Modern Intersection Observer implementation
    const observerOptions = {
      threshold: [0, 0.1, 0.2, 0.3], // Multiple thresholds for better control
      rootMargin: "0px 0px -20% 0px", // Trigger when 20% visible
    };

    const animationObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        // Trigger animation when element is 20% visible
        if (entry.isIntersecting && entry.intersectionRatio >= 0.12) {
          entry.target.classList.add("active");

          // Optional: Stop observing once animated (for performance)
          // animationObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe all animation elements
    const animationElements = document.querySelectorAll(
      ".reveal-on-scroll, .fade-in, .fade-in-up, .slide-up, .slide-in-left, .slide-in-right, .scale-in, .scale-in-up"
    );

    animationElements.forEach((element) => {
      animationObserver.observe(element);
    });
  };

  // Legacy function for backward compatibility
  const checkRevealAnimations = () => {
    revealElements.forEach((element) => {
      const elementTop = element.getBoundingClientRect().top;
      const viewportHeight = window.innerHeight;

      // Updated threshold to 20% visibility
      if (elementTop < viewportHeight * 0.8) {
        element.classList.add("active");
      }
    });
  };

  const handleScroll = () => {
    // Handle scroll start detection for smooth parallax transitions
    handleScrollStart();

    // Use requestAnimationFrame for smooth performance
    if (!ticking) {
      requestAnimationFrame(() => {
        updateParallax();
        // Only call checkRevealAnimations if Intersection Observer is not supported
        if (!window.IntersectionObserver) {
          checkRevealAnimations();
        }
        ticking = false;
      });
      ticking = true;
    }

    // Navbar styling based on scroll position
    if (window.pageYOffset > 50) {
      navbar.style.backgroundColor = "var(--color-blur-bg)";
      navbar.style.backdropFilter = "blur(12px)";
      navbar.style.webkitBackdropFilter = "blur(12px)";
      navbar.style.boxShadow = "0 5px 20px rgba(0, 0, 0, 0.1)";
      navbar.style.borderBottom = "1px solid var(--color-border-subtle)";
      navbar.style.paddingTop = `${initialNavbarPaddingTop * 0.7}px`;
      navbar.style.paddingBottom = `${initialNavbarPaddingBottom * 0.7}px`;
    } else {
      navbar.style.backgroundColor = "transparent";
      navbar.style.backdropFilter = "none";
      navbar.style.webkitBackdropFilter = "none";
      navbar.style.boxShadow = "none";
      navbar.style.borderBottom = "1px solid transparent";
      navbar.style.paddingTop = `${initialNavbarPaddingTop}px`;
      navbar.style.paddingBottom = `${initialNavbarPaddingBottom}px`;
    }
  };

  // Initialize parallax elements with transitions enabled
  const initializeParallax = () => {
    enableParallaxTransitions();
    updateParallax();
  };

  window.addEventListener("scroll", handleScroll);
  window.addEventListener("resize", handleScroll);

  // Initialize the parallax system
  initializeParallax();

  // Initialize enhanced scroll-triggered animations
  initializeScrollAnimations();

  // Initialize reveal animations on page load (fallback for non-Intersection Observer browsers)
  if (!window.IntersectionObserver) {
    checkRevealAnimations();
  }

  // Initialize navbar in transparent state
  const initializeNavbar = () => {
    navbar.style.backgroundColor = "transparent";
    navbar.style.backdropFilter = "none";
    navbar.style.webkitBackdropFilter = "none";
    navbar.style.boxShadow = "none";
    navbar.style.borderBottom = "1px solid transparent";
    navbar.style.paddingTop = `${initialNavbarPaddingTop}px`;
    navbar.style.paddingBottom = `${initialNavbarPaddingBottom}px`;
  };

  // Initialize navbar state
  initializeNavbar();

  // Mobile menu toggle functionality
  const initializeMobileMenu = () => {
    const mobileMenuToggle = document.getElementById("mobileMenuToggle");
    const navbarLinks = document.getElementById("navbarLinks");

    if (mobileMenuToggle && navbarLinks) {
      // Toggle mobile menu
      mobileMenuToggle.addEventListener("click", function (e) {
        e.preventDefault();
        e.stopPropagation();

        const isActive = this.classList.contains("active");

        if (isActive) {
          this.classList.remove("active");
          navbarLinks.classList.remove("active");
        } else {
          this.classList.add("active");
          navbarLinks.classList.add("active");
        }
      });

      // Close mobile menu when clicking on a nav link
      navbarLinks.addEventListener("click", function (e) {
        if (e.target.classList.contains("nav-link")) {
          mobileMenuToggle.classList.remove("active");
          navbarLinks.classList.remove("active");
        }
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (e) {
        if (
          !mobileMenuToggle.contains(e.target) &&
          !navbarLinks.contains(e.target)
        ) {
          mobileMenuToggle.classList.remove("active");
          navbarLinks.classList.remove("active");
        }
      });

      // Close mobile menu on window resize if it's open
      window.addEventListener("resize", function () {
        if (window.innerWidth > 768) {
          mobileMenuToggle.classList.remove("active");
          navbarLinks.classList.remove("active");
        }
      });
    }
  };

  // Initialize mobile menu
  initializeMobileMenu();

  document.querySelectorAll(".scroll-link").forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href");
      document.querySelector(targetId).scrollIntoView({
        behavior: "smooth",
      });
    });
  });

  const contactFormContainer = document.querySelector(
    ".contact-form-container"
  );
  if (contactFormContainer) {
    if (
      window.location.hash === "#contact" &&
      (document.querySelector(".alert-success") ||
        document.querySelector(".alert-danger"))
    ) {
      const contactSection = document.getElementById("contact");
      if (contactSection) {
        contactSection.scrollIntoView({ behavior: "smooth" });
      }
    }
  }

  const closeAlertButtons = document.querySelectorAll(".close-alert");
  closeAlertButtons.forEach((button) => {
    button.addEventListener("click", function () {
      this.closest(".alert").style.display = "none";
    });
  });

  // Portfolio Slideshow Functionality
  class PortfolioSlideshow {
    constructor(element) {
      this.slideshow = element;
      this.slides = element.querySelector(".slideshow-slides");
      this.slideElements = element.querySelectorAll(".slideshow-slide");
      this.prevBtn = element.querySelector(".slideshow-nav.prev");
      this.nextBtn = element.querySelector(".slideshow-nav.next");
      this.dots = element.querySelectorAll(".slideshow-dot");
      this.currentSlide = 0;
      this.totalSlides = this.slideElements.length;
      this.autoAdvanceInterval = null;
      this.autoAdvanceDelay = 5000; // 5 seconds

      this.init();
    }

    init() {
      // Add event listeners
      this.prevBtn.addEventListener("click", () => this.prevSlide());
      this.nextBtn.addEventListener("click", () => this.nextSlide());

      this.dots.forEach((dot, index) => {
        dot.addEventListener("click", () => this.goToSlide(index));
      });

      // Add keyboard support
      this.slideshow.addEventListener("keydown", (e) => {
        if (e.key === "ArrowLeft") {
          this.prevSlide();
        } else if (e.key === "ArrowRight") {
          this.nextSlide();
        }
      });

      // Make slideshow focusable for keyboard navigation
      this.slideshow.setAttribute("tabindex", "0");

      // Start auto-advance
      this.startAutoAdvance();

      // Pause auto-advance on hover
      this.slideshow.addEventListener("mouseenter", () => {
        this.stopAutoAdvance();
        this.slideshow.classList.add("paused");
      });
      this.slideshow.addEventListener("mouseleave", () => {
        this.startAutoAdvance();
        this.slideshow.classList.remove("paused");
      });

      // Touch/swipe support for mobile
      this.addTouchSupport();
    }

    goToSlide(slideIndex) {
      this.currentSlide = slideIndex;
      const translateX = -slideIndex * 20; // 20% per slide (100% / 5 slides)
      this.slides.style.transform = `translateX(${translateX}%)`;

      // Update dots
      this.dots.forEach((dot, index) => {
        dot.classList.toggle("active", index === slideIndex);
      });
    }

    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
      this.goToSlide(this.currentSlide);
    }

    prevSlide() {
      this.currentSlide =
        (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
      this.goToSlide(this.currentSlide);
    }

    startAutoAdvance() {
      this.stopAutoAdvance(); // Clear any existing interval
      this.autoAdvanceInterval = setInterval(() => {
        this.nextSlide();
      }, this.autoAdvanceDelay);
    }

    stopAutoAdvance() {
      if (this.autoAdvanceInterval) {
        clearInterval(this.autoAdvanceInterval);
        this.autoAdvanceInterval = null;
      }
    }

    addTouchSupport() {
      let startX = 0;
      let endX = 0;

      this.slideshow.addEventListener("touchstart", (e) => {
        startX = e.touches[0].clientX;
      });

      this.slideshow.addEventListener("touchend", (e) => {
        endX = e.changedTouches[0].clientX;
        const diff = startX - endX;

        // Minimum swipe distance
        if (Math.abs(diff) > 50) {
          if (diff > 0) {
            this.nextSlide();
          } else {
            this.prevSlide();
          }
        }
      });
    }
  }

  // Initialize all slideshows
  const slideshows = document.querySelectorAll(".portfolio-slideshow");
  slideshows.forEach((slideshow) => {
    new PortfolioSlideshow(slideshow);
  });
});
