:root {
  --color-light-bg: #cde5f8;
  --color-light-bg-alt: #f8f8f8;
  --color-text-dark: #212121;
  --color-text-muted: #616161;
  --color-accent: #d32f2f;
  --color-accent-darker: #b71c1c;
  --color-secondary-accent: #1976d2;
  --color-secondary-accent-darker: #0d47a1;
  --color-blur-bg: rgba(255, 255, 255, 0.85);
  --color-form-bg: rgba(255, 255, 255, 0.95);
  --color-border-subtle: rgba(0, 0, 0, 0.1);
  --color-error: #ef5350;
  --color-success: #66bb6a;
  --font-primary: "IBM Plex Sans", sans-serif;
  --font-brand: "Orbitron", sans-serif;
  --transition-speed: 0.4s ease-in-out;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-light-bg);
  color: var(--color-text-dark);
  font-family: var(--font-primary);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overscroll-behavior-y: none;
}

a {
  color: var(--color-accent);
  text-decoration: none;
  transition: color var(--transition-speed);
}

a:hover {
  color: var(--color-accent-darker);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 30px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
}

.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 0;
  transition: background-color var(--transition-speed),
    backdrop-filter var(--transition-speed);
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border-bottom: 1px solid transparent;
  border-radius: 10px;
  padding: 15px 30px;
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: none;
  transition: all var(--transition-speed);
}

.navbar-brand {
  font-family: var(--font-brand);
  font-size: 1.6em;
  font-weight: 600;
  color: var(--color-text-dark);
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  transition: color var(--transition-speed);
}

.navbar-brand:hover {
  color: var(--color-accent);
}

.navbar-logo {
  height: 40px;
  margin-right: 10px;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.1));
  transition: filter var(--transition-speed);
}

.navbar-brand:hover .navbar-logo {
  filter: drop-shadow(0 0 8px var(--color-accent));
}

.brand-accent {
  color: var(--color-accent);
  font-size: 0.9em;
  margin: 0 2px;
}

.navbar-links {
  display: flex;
  gap: 30px;
}

.nav-link {
  color: var(--color-text-muted);
  font-size: 1em;
  font-weight: 400;
  padding: 5px 0;
  position: relative;
  transition: color var(--transition-speed);
}

.nav-link:hover {
  color: var(--color-text-dark);
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 1px;
  display: block;
  margin-top: 5px;
  right: 0;
  background: var(--color-accent);
  transition: width var(--transition-speed) ease;
  -webkit-transition: width var(--transition-speed) ease;
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
  background: var(--color-accent);
}

.nav-button {
  background-color: var(--color-accent);
  color: var(--color-light-bg);
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 600;
  transition: background-color var(--transition-speed),
    color var(--transition-speed), transform var(--transition-speed),
    box-shadow var(--transition-speed);
  box-shadow: 0 2px 10px rgba(211, 47, 47, 0.1);
}

.nav-button:hover {
  background-color: var(--color-accent-darker);
  color: var(--color-light-bg);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(211, 47, 47, 0.3);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
  position: relative;
  outline: none;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background-color: var(--color-text-dark);
  border-radius: 2px;
  transition: all var(--transition-speed);
  transform-origin: center;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu-toggle:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
  border-radius: 3px;
}

.footer {
  background-color: var(--color-light-bg-alt);
  color: var(--color-text-muted);
  padding: 30px 0;
  font-size: 0.9em;
  border-top: 1px solid var(--color-border-subtle);
}

.footer .container {
  display: flex;
  justify-content: center;
  align-items: center;
}

main {
  padding-top: 100px;
}

@media (max-width: 992px) {
  .navbar {
    padding: 15px 20px;
  }

  .navbar-links {
    gap: 20px;
  }

  .nav-button {
    padding: 8px 15px;
    font-size: 0.9em;
  }

  .navbar-brand {
    font-size: 1.4em;
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: 10px 0;
  }

  .navbar {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 15px 15px;
    border-radius: 0;
    position: relative;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .navbar-links {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--color-blur-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-top: 1px solid var(--color-border-subtle);
    flex-direction: column;
    gap: 10px;
    width: 100%;
    text-align: center;
    padding: 20px 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  }

  .navbar-links.active {
    display: flex;
  }

  .nav-link {
    padding: 10px 15px;
    font-size: 0.95em;
    border-radius: 5px;
    transition: background-color var(--transition-speed);
  }

  .nav-link:hover {
    background-color: rgba(211, 47, 47, 0.1);
  }

  .nav-button {
    width: fit-content;
    margin: 10px auto 0;
  }

  main {
    padding-top: 100px;
  }

  .navbar-logo {
    height: 35px;
  }
}

.landing-page {
  position: relative;
  overflow: hidden;
}

.parallax-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 80px 0;
  overflow: hidden;
  transform-style: preserve-3d;
}

/* Parallax elements smooth transition styles */
[data-parallax-element] {
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Disable transitions during active scrolling for immediate parallax response */
[data-parallax-element].scrolling {
  transition: none;
}

.hero-section {
  min-height: 100vh;
  background-color: var(--color-light-bg);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0;
  z-index: 1;
  overflow: hidden;
}

.hero-interactive-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.hero-content {
  z-index: 2;
  padding: 0 20px;
  perspective: 1000px;
  text-align: center;
}

.hero-title {
  font-family: var(--font-brand);
  font-size: 4.8em;
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: 3px;
  margin-bottom: 20px;
  color: var(--color-text-dark);
  text-shadow: 0 0 15px rgba(211, 47, 47, 0.2), 0 0 30px rgba(25, 118, 210, 0.1);
}

.hero-title .text-accent {
  color: var(--color-accent);
}

.hero-title .text-light {
  color: var(--color-text-dark);
}

.hero-subtitle {
  font-family: var(--font-primary);
  font-size: 1.4em;
  color: var(--color-text-muted);
  max-width: 800px;
  margin: 0 auto 50px auto;
  line-height: 1.5;
}

.hero-scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--color-text-muted);
  font-size: 0.9em;
  z-index: 2;
  animation: bounce 2s infinite;
}

.mouse-icon {
  width: 26px;
  height: 40px;
  border: 2px solid var(--color-accent);
  border-radius: 15px;
  margin: 10px auto 0;
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background-color: var(--color-accent);
  border-radius: 4px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll 1.5s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }

  40% {
    transform: translateX(-50%) translateY(-10px);
  }

  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

@keyframes scroll {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }

  100% {
    transform: translateX(-50%) translateY(20px);
    opacity: 0;
  }
}

.btn {
  display: inline-block;
  padding: 14px 30px;
  border-radius: 8px;
  font-weight: 600;
  transition: all var(--transition-speed);
  text-transform: uppercase;
  letter-spacing: 1.5px;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-primary {
  background-color: var(--color-accent);
  color: var(--color-light-bg);
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: all var(--transition-speed) ease-in-out;
  z-index: -1;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background-color: var(--color-accent-darker);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(211, 47, 47, 0.4);
}

.section-title {
  font-family: var(--font-brand);
  font-size: 3.5em;
  font-weight: 600;
  margin-bottom: 40px;
  color: var(--color-text-dark);
  text-align: center;
  position: relative;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.section-title::after {
  content: "";
  display: block;
  width: 80px;
  height: 3px;
  background-color: var(--color-accent);
  margin: 20px auto 0 auto;
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.2em;
  color: var(--color-text-muted);
  text-align: center;
  margin-bottom: 60px;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.services-section {
  background: linear-gradient(
    135deg,
    var(--color-light-bg) 0%,
    var(--color-light-bg-alt) 100%
  );
  min-height: 80vh;
  display: flex;
  align-items: center;
  padding: 60px 0;
  position: relative;
}

.services-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 50px;
  text-align: center;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  text-align: center;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--color-accent) 0%,
    var(--color-secondary-accent) 100%
  );
  transform: scaleX(0);
  transition: transform var(--transition-speed) ease;
}

.service-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: rgba(211, 47, 47, 0.3);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-icon-wrapper {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  background: linear-gradient(
    135deg,
    var(--color-accent) 0%,
    var(--color-secondary-accent) 100%
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3);
  transition: transform var(--transition-speed) ease;
}

.service-card:hover .service-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.service-icon {
  font-size: 2.5em;
  filter: grayscale(1) brightness(0) invert(1);
}

.service-card h3 {
  font-family: var(--font-brand);
  font-size: 1.5em;
  margin-bottom: 20px;
  color: var(--color-text-dark);
  letter-spacing: 0.5px;
  font-weight: 600;
}

.service-card p {
  color: var(--color-text-muted);
  line-height: 1.7;
  font-size: 0.95em;
}

.portfolio-section {
  background-color: var(--color-light-bg);
  min-height: 90vh;
}

.portfolio-grid {
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.portfolio-item {
  display: flex;
  align-items: center;
  gap: 70px;
  background-color: var(--color-light-bg-alt);
  border-radius: 18px;
  overflow: hidden;
  padding: 50px;
  border: 1px solid var(--color-border-subtle);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
  transition: transform var(--transition-speed),
    box-shadow var(--transition-speed), border-color var(--transition-speed);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  border-color: var(--color-accent);
}

.portfolio-item.reverse {
  flex-direction: row-reverse;
}

.portfolio-image-placeholder {
  flex: 1.8;
  min-width: 700px;
  max-width: 70%;
  min-height: 500px;
  border-radius: 15px;
  overflow: hidden;
  background-color: #ddd;
  position: relative;
  box-shadow: 0 12px 50px rgba(0, 0, 0, 0.3);
}

.portfolio-image-placeholder img {
  border-radius: 10px;
  object-fit: cover;
  filter: brightness(0.95);
  transition: filter var(--transition-speed);
  width: 100%;
  height: 100%;
}

.portfolio-item:hover .portfolio-image-placeholder img {
  filter: brightness(1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.2) 100%
  );
  z-index: 1;
  pointer-events: none;
}

/* Portfolio Slideshow Styles */
.portfolio-slideshow {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  transform: scale(1);
  transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-slideshow {
  transform: scale(1.02);
}

.slideshow-container {
  position: relative;
  width: 100%;
}

.slideshow-slides {
  display: flex;
  width: 500%;
  transition: transform 0.5s ease-in-out;
}

.slideshow-slide {
  width: 20%;
  position: relative;
}

.slideshow-slide img {
  width: 100%;
  min-height: 500px;
  height: auto;
  display: block;
  border-radius: 15px;
  object-fit: cover;
  filter: brightness(0.98) contrast(1.08) saturate(1.15);
  transition: filter var(--transition-speed), transform 0.3s ease;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.slideshow-slide:hover img {
  filter: brightness(1) contrast(1.15) saturate(1.2);
}

.portfolio-item:hover .slideshow-slide img {
  filter: brightness(1);
}

/* Navigation Arrows */
.slideshow-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  font-weight: bold;
  z-index: 10;
  transition: all var(--transition-speed);
  opacity: 0;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.portfolio-item:hover .slideshow-nav {
  opacity: 1;
}

.slideshow-nav:hover {
  background-color: var(--color-accent);
  transform: translateY(-50%) scale(1.15);
  box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

.slideshow-nav.prev {
  left: 20px;
}

.slideshow-nav.next {
  right: 20px;
}

/* Dot Indicators */
.slideshow-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.4);
  padding: 10px 16px;
  border-radius: 25px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.slideshow-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all var(--transition-speed);
  border: 2px solid transparent;
}

.slideshow-dot.active {
  background-color: var(--color-accent);
  border-color: white;
  transform: scale(1.2);
}

.slideshow-dot:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

/* Slideshow Loading State */
.portfolio-slideshow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-slideshow.loading::before {
  opacity: 1;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth transitions for better UX */

.slideshow-nav:active {
  transform: translateY(-50%) scale(0.95);
}

/* Focus states for accessibility */
.slideshow-nav:focus,
.slideshow-dot:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Pause indicator */
.portfolio-slideshow.paused .slideshow-indicators::after {
  content: "⏸";
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--color-accent);
  font-size: 14px;
  opacity: 0.7;
}

.portfolio-details {
  flex: 1;
  text-align: left;
}

.portfolio-details h3 {
  font-family: var(--font-brand);
  font-size: 2.2em;
  color: var(--color-secondary-accent);
  margin-bottom: 20px;
  letter-spacing: 0.8px;
  text-shadow: 0 0 8px rgba(25, 118, 210, 0.15);
}

.portfolio-details p {
  color: var(--color-text-dark);
  margin-bottom: 25px;
  line-height: 1.7;
}

.portfolio-details ul {
  list-style: none;
  padding: 0;
}

.portfolio-details ul li {
  color: var(--color-text-muted);
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  font-size: 1em;
}

.list-icon {
  color: var(--color-accent);
  margin-right: 12px;
  font-weight: bold;
  flex-shrink: 0;
  font-size: 1.2em;
}

.portfolio-actions {
  margin-top: 30px;
  padding-top: 25px;
  border-top: 1px solid var(--color-border-subtle);
}

.btn-demo {
  background: linear-gradient(
    135deg,
    var(--color-secondary-accent) 0%,
    var(--color-secondary-accent-darker) 100%
  );
  color: white;
  padding: 14px 28px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1em;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all var(--transition-speed);
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-demo::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: all var(--transition-speed) ease-in-out;
  z-index: 1;
}

.btn-demo:hover::before {
  left: 100%;
}

.btn-demo:hover {
  background: linear-gradient(
    135deg,
    var(--color-secondary-accent-darker) 0%,
    var(--color-secondary-accent) 100%
  );
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
}

.contact-section {
  background: linear-gradient(
    135deg,
    var(--color-light-bg-alt) 0%,
    var(--color-light-bg) 100%
  );
  min-height: 90vh;
  display: flex;
  align-items: center;
  padding: 60px 0;
  position: relative;
}

.contact-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(211, 47, 47, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(25, 118, 210, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.contact-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.contact-header {
  margin-bottom: 50px;
}

.contact-header .section-title {
  font-size: 3em;
  margin-bottom: 20px;
  background: linear-gradient(
    135deg,
    var(--color-accent) 0%,
    var(--color-secondary-accent) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-header .section-subtitle {
  font-size: 1.2em;
  color: var(--color-text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.contact-form-wrapper {
  position: relative;
}

.contact-form-container {
  max-width: 600px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 50px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.contact-form-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--color-accent) 0%,
    var(--color-secondary-accent) 100%
  );
  transform: scaleX(0);
  transition: transform var(--transition-speed) ease;
}

.contact-form-container:hover {
  transform: translateY(-8px) scale(1.01);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
  border-color: rgba(211, 47, 47, 0.3);
}

.contact-form-container:hover::before {
  transform: scaleX(1);
}

.contact-form .form-group {
  margin-bottom: 25px;
  text-align: left;
}

.contact-form label {
  display: block;
  margin-bottom: 10px;
  color: var(--color-text-dark);
  font-weight: 300;
  font-size: 1.05em;
  letter-spacing: 0.5px;
}

.contact-form .form-control {
  width: 100%;
  padding: 14px 18px;
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--color-border-subtle);
  border-radius: 8px;
  color: var(--color-text-dark);
  font-family: var(--font-primary);
  font-size: 1em;
  transition: border-color var(--transition-speed),
    background-color var(--transition-speed), box-shadow var(--transition-speed);
}

.contact-form .form-control:focus {
  outline: none;
  border-color: var(--color-accent);
  background-color: rgba(211, 47, 47, 0.05);
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.contact-form textarea.form-control {
  resize: vertical;
  min-height: 150px;
}

.contact-form .text-danger {
  color: var(--color-error);
  font-size: 0.88em;
  margin-top: 5px;
  display: block;
  font-weight: 400;
}

.contact-form .validation-summary {
  background-color: rgba(239, 83, 80, 0.1);
  color: var(--color-error);
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 25px;
  border: 1px solid var(--color-error);
  font-size: 0.9em;
}

.btn-submit {
  width: 100%;
  background-color: var(--color-accent);
  color: var(--color-light-bg);
  padding: 16px 25px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.2em;
  transition: background-color var(--transition-speed),
    transform var(--transition-speed), box-shadow var(--transition-speed);
  box-shadow: 0 5px 20px rgba(211, 47, 47, 0.2);
}

.btn-submit:hover {
  background-color: var(--color-accent-darker);
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(211, 47, 47, 0.4);
}

.alert {
  padding: 18px 25px;
  border-radius: 10px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  z-index: 1;
  font-size: 1em;
  line-height: 1.4;
}

.alert-success {
  background-color: rgba(102, 187, 106, 0.15);
  border: 1px solid var(--color-success);
  color: var(--color-success);
}

.alert-danger {
  background-color: rgba(239, 83, 80, 0.15);
  border: 1px solid var(--color-error);
  color: var(--color-error);
}

.message-blurview {
  background-color: var(--color-blur-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--color-border-subtle);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.close-alert {
  background: none;
  border: none;
  font-size: 1.6em;
  color: inherit;
  cursor: pointer;
  line-height: 1;
  padding: 0;
  margin-left: 20px;
  transition: transform var(--transition-speed);
}

.close-alert:hover {
  transform: rotate(90deg) scale(1.1);
}

@media (max-width: 992px) {
  .hero-title {
    font-size: 3.5em;
    letter-spacing: 2px;
  }

  .hero-subtitle {
    font-size: 1.2em;
  }

  .section-title {
    font-size: 3em;
    letter-spacing: 1.5px;
  }

  .services-grid {
    gap: 40px;
  }

  .service-card {
    padding: 35px 25px;
  }

  .service-icon-wrapper {
    width: 70px;
    height: 70px;
  }

  .service-icon {
    font-size: 2.2em;
  }

  .portfolio-item {
    flex-direction: column;
    gap: 40px;
    text-align: center;
    padding: 30px;
  }

  .portfolio-item.reverse {
    flex-direction: column;
  }

  .portfolio-image-placeholder {
    min-width: unset;
    max-width: 100%;
    min-height: 400px;
    flex: 1;
  }

  .slideshow-slide img {
    min-height: 400px;
  }

  .slideshow-nav {
    width: 42px;
    height: 42px;
    font-size: 18px;
    opacity: 1;
  }

  .slideshow-nav.prev {
    left: 15px;
  }

  .slideshow-nav.next {
    right: 15px;
  }

  .slideshow-indicators {
    bottom: 15px;
    padding: 8px 14px;
  }

  .slideshow-dot {
    width: 12px;
    height: 12px;
  }

  .portfolio-details {
    text-align: center;
  }

  .portfolio-details h3 {
    font-size: 2em;
  }

  .portfolio-details ul {
    justify-content: center;
    gap: 10px 20px;
  }

  .portfolio-details ul li {
    width: auto;
  }

  .contact-form-container {
    padding: 40px;
  }

  .contact-header .section-title {
    font-size: 2.5em;
  }

  .contact-header .section-subtitle {
    font-size: 1.1em;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2.5em;
    letter-spacing: 1.5px;
  }

  .hero-subtitle {
    font-size: 1em;
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 2.2em;
    letter-spacing: 1px;
    margin-bottom: 30px;
  }

  .section-title::after {
    width: 50px;
    height: 2px;
    margin-top: 15px;
  }

  .section-subtitle {
    font-size: 0.95em;
    margin-bottom: 40px;
  }

  .services-grid {
    gap: 30px;
  }

  .service-card {
    padding: 25px 20px;
  }

  .service-icon-wrapper {
    width: 60px;
    height: 60px;
  }

  .service-icon {
    font-size: 2em;
  }

  .service-card h3 {
    font-size: 1.3em;
  }

  .portfolio-item {
    padding: 20px;
    gap: 30px;
  }

  .portfolio-image-placeholder {
    min-width: unset;
    max-width: 100%;
    min-height: 350px;
    margin-bottom: 20px;
  }

  .slideshow-slide img {
    min-height: 350px;
  }

  .slideshow-nav {
    width: 38px;
    height: 38px;
    font-size: 16px;
  }

  .slideshow-nav.prev {
    left: 12px;
  }

  .slideshow-nav.next {
    right: 12px;
  }

  .slideshow-indicators {
    bottom: 12px;
    padding: 6px 12px;
  }

  .slideshow-dot {
    width: 10px;
    height: 10px;
  }

  .portfolio-details h3 {
    font-size: 1.6em;
  }

  .contact-form-container {
    padding: 30px 20px;
  }

  .contact-header .section-title {
    font-size: 2em;
  }

  .contact-header .section-subtitle {
    font-size: 1em;
  }

  .contact-form label {
    font-size: 0.95em;
  }

  .contact-form .form-control {
    padding: 10px 12px;
  }

  .btn-submit {
    padding: 12px 20px;
    font-size: 1em;
  }

  .alert {
    font-size: 0.9em;
    padding: 12px 18px;
  }

  .close-alert {
    font-size: 1.4em;
  }

  main {
    padding-top: 90px;
  }

  .navbar {
    padding: 10px 15px;
  }

  .mobile-menu-toggle {
    width: 28px;
    height: 28px;
  }

  .hamburger-line {
    height: 2px;
  }

  .navbar-links {
    padding: 15px;
  }

  .nav-link {
    padding: 8px 12px;
    font-size: 0.9em;
  }
}

/* Simplified Scroll-Triggered Animation Classes */

/* Section-level animations with smooth professional easing */
.section-animate {
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-animate.active {
  opacity: 1;
  transform: translateY(0);
}

/* Fade in animation for simple content */
.fade-in {
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in.active {
  opacity: 1;
}

/* Slide in from left for alternating content */
.slide-in-left {
  opacity: 0;
  transform: translateX(-60px);
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-left.active {
  opacity: 1;
  transform: translateX(0);
}

/* Slide in from right for alternating content */
.slide-in-right {
  opacity: 0;
  transform: translateX(60px);
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-right.active {
  opacity: 1;
  transform: translateX(0);
}

/* Scale animation for special elements */
.scale-in {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scale-in.active {
  opacity: 1;
  transform: scale(1);
}

/* Simplified delay classes for staggered section animations */
.delay-200 {
  transition-delay: 0.2s;
}

.delay-400 {
  transition-delay: 0.4s;
}

.delay-600 {
  transition-delay: 0.6s;
}

/* Legacy support for existing reveal-on-scroll class */
.reveal-on-scroll {
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reveal-on-scroll.active {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive animation adjustments */
@media (max-width: 768px) {
  .section-animate,
  .reveal-on-scroll,
  .fade-in,
  .slide-in-left,
  .slide-in-right,
  .scale-in {
    transition-duration: 0.6s; /* Slightly faster on mobile */
  }

  .slide-in-left {
    transform: translateX(-30px); /* Reduced distance on mobile */
  }

  .slide-in-right {
    transform: translateX(30px); /* Reduced distance on mobile */
  }
}

/* Reduced motion preference support */
@media (prefers-reduced-motion: reduce) {
  .section-animate,
  .reveal-on-scroll,
  .fade-in,
  .slide-in-left,
  .slide-in-right,
  .scale-in {
    transition-duration: 0.2s;
    transform: none !important;
  }

  .section-animate.active,
  .reveal-on-scroll.active,
  .fade-in.active,
  .slide-in-left.active,
  .slide-in-right.active,
  .scale-in.active {
    transform: none !important;
  }
}

.delay-300 {
  transition-delay: 0.3s;
}

.delay-350 {
  transition-delay: 0.35s;
}

.delay-400 {
  transition-delay: 0.4s;
}

.delay-450 {
  transition-delay: 0.45s;
}

.delay-500 {
  transition-delay: 0.5s;
}

.delay-600 {
  transition-delay: 0.6s;
}

.delay-700 {
  transition-delay: 0.7s;
}

.delay-800 {
  transition-delay: 0.8s;
}

.delay-900 {
  transition-delay: 0.9s;
}

.delay-750 {
  transition-delay: 0.75s;
}

.delay-800 {
  transition-delay: 0.8s;
}

.delay-850 {
  transition-delay: 0.85s;
}

.delay-900 {
  transition-delay: 0.9s;
}

.delay-950 {
  transition-delay: 0.95s;
}

.delay-1000 {
  transition-delay: 1s;
}

/* Fast Animation Variants */
.fast {
  transition-duration: 0.3s !important;
}

.faster {
  transition-duration: 0.2s !important;
}

.slow {
  transition-duration: 0.8s !important;
}

.slower {
  transition-delay: 1.2s !important;
}

/* Footer Styles */
.main-footer {
  background: linear-gradient(135deg, var(--color-text-dark) 0%, #1a1a1a 100%);
  color: var(--color-light-bg);
  padding: 60px 0 0;
  margin-top: 80px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  color: var(--color-accent);
  font-family: var(--font-brand);
  font-size: 1.2em;
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer-brand {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-logo {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}

.footer-brand-text {
  font-family: var(--font-brand);
  font-size: 1.5em;
  font-weight: 600;
  color: var(--color-light-bg);
}

.footer-description {
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #cccccc;
  text-decoration: none;
  transition: color var(--transition-speed);
  font-size: 0.95em;
}

.footer-links a:hover {
  color: var(--color-accent);
}

.footer-contact p {
  color: #cccccc;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  font-size: 0.95em;
}

.contact-icon {
  margin-right: 10px;
  font-size: 1.1em;
}

.footer-social {
  margin-top: 20px;
  display: flex;
  gap: 15px;
}

.social-link {
  color: #cccccc;
  text-decoration: none;
  padding: 8px 12px;
  border: 1px solid #444;
  border-radius: 5px;
  font-size: 0.9em;
  transition: all var(--transition-speed);
}

.social-link:hover {
  color: var(--color-accent);
  border-color: var(--color-accent);
  background-color: rgba(211, 47, 47, 0.1);
}

.footer-bottom {
  border-top: 1px solid #444;
  padding: 25px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  margin: 0;
  color: #999;
  font-size: 0.9em;
}

.footer-bottom-links {
  display: flex;
  gap: 20px;
}

.footer-bottom-links a {
  color: #999;
  text-decoration: none;
  font-size: 0.9em;
  transition: color var(--transition-speed);
}

.footer-bottom-links a:hover {
  color: var(--color-accent);
}

/* Footer Responsive Styles */
@media (max-width: 992px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }
}

@media (max-width: 576px) {
  .main-footer {
    padding: 40px 0 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .footer-bottom-links {
    justify-content: center;
  }

  .footer-social {
    justify-content: center;
  }
}
