@model deeplab_systems.Models.ContactViewModel
@{
    ViewData["Title"] = "Home - Deeplab Systems";
}

<div class="landing-page">

    <section class="hero-section" data-parallax-container>
        <div id="hero-interactive-background" class="hero-interactive-background"></div>
        <div class="hero-content section-animate">
            <h1 class="hero-title">
                <span class="text-accent">Innovate.</span> <br>
                <span class="text-light">Build.</span> <br>
                <span class="text-accent">Transform.</span>
            </h1>
            <p class="hero-subtitle">
                At Deeplab Systems, we craft cutting-edge software solutions that redefine possibilities.
            </p>
            <a href="#contact" class="btn btn-primary scroll-link">Get a Quote</a>
        </div>
        <div class="hero-scroll-indicator section-animate delay-400">
            <span>Scroll Down</span>
            <div class="mouse-icon">
                <div class="scroll-wheel"></div>
            </div>
        </div>
    </section>

    <section id="services" class="services-section">
        <div class="container">
            <div class="services-content section-animate">
                <h2 class="section-title">Our Services</h2>
                <p class="section-subtitle">Transforming ideas into powerful digital solutions</p>
                <div class="services-grid">
                    <div class="service-card scale-in delay-200">
                        <div class="service-icon-wrapper">
                            <span class="service-icon">🌐</span>
                        </div>
                        <h3>Web Development</h3>
                        <p>From complex enterprise applications to beautiful user interfaces, we build scalable and secure web solutions that drive business growth.</p>
                    </div>
                    <div class="service-card scale-in delay-400">
                        <div class="service-icon-wrapper">
                            <span class="service-icon">📱</span>
                        </div>
                        <h3>Native Mobile Apps</h3>
                        <p>Deliver exceptional user experiences on iOS and Android with high-performance, feature-rich native applications that engage users.</p>
                    </div>
                    <div class="service-card scale-in delay-600">
                        <div class="service-icon-wrapper">
                            <span class="service-icon">🛠️</span>
                        </div>
                        <h3>Custom Solutions</h3>
                        <p>We tailor software precisely to your unique business needs, integrating seamlessly with your existing infrastructure and workflows.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="portfolio" class="portfolio-section">
        <div class="container">
            <h2 class="section-title section-animate">Our Work</h2>
            <div class="portfolio-grid">
                <div class="portfolio-item slide-in-left">
                    <div class="portfolio-image-placeholder">
                        <div class="portfolio-slideshow" data-slideshow="erp">
                            <div class="slideshow-container">
                                <div class="slideshow-slides">
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="E-Learning ERP Screenshot 1" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="E-Learning ERP Screenshot 2" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="E-Learning ERP Screenshot 3" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="E-Learning ERP Screenshot 4" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="E-Learning ERP Screenshot 5" />
                                    </div>
                                </div>
                                <button class="slideshow-nav prev" data-direction="prev">‹</button>
                                <button class="slideshow-nav next" data-direction="next">›</button>
                                <div class="slideshow-indicators">
                                    <span class="slideshow-dot active" data-slide="0"></span>
                                    <span class="slideshow-dot" data-slide="1"></span>
                                    <span class="slideshow-dot" data-slide="2"></span>
                                    <span class="slideshow-dot" data-slide="3"></span>
                                    <span class="slideshow-dot" data-slide="4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="image-overlay"></div>
                    </div>
                    <div class="portfolio-details">
                        <h3>Smart School ERP</h3>
                        <p>Our flagship solution: a comprehensive enterprise resource planning system tailored for educational institutions, streamlining administration, student management, and online learning.</p>
                        <ul>
                            <li><span class="list-icon">✓</span> Student & Faculty Management</li>
                            <li><span class="list-icon">✓</span> Course & Curriculum Tracking</li>
                            <li><span class="list-icon">✓</span> Online Learning Platform</li>
                            <li><span class="list-icon">✓</span> Reporting & Analytics</li>
                        </ul>
                        <div class="portfolio-actions">
                            <a href="#contact" class="btn btn-demo scroll-link">Request Demo Access</a>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item reverse slide-in-right">
                    <div class="portfolio-image-placeholder">
                        <div class="portfolio-slideshow" data-slideshow="tailored">
                            <div class="slideshow-container">
                                <div class="slideshow-slides">
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="Tailored Applications Screenshot 1" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="Tailored Applications Screenshot 2" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="Tailored Applications Screenshot 3" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="Tailored Applications Screenshot 4" />
                                    </div>
                                    <div class="slideshow-slide">
                                        <img src="~/images/slide1.png" alt="Tailored Applications Screenshot 5" />
                                    </div>
                                </div>
                                <button class="slideshow-nav prev" data-direction="prev">‹</button>
                                <button class="slideshow-nav next" data-direction="next">›</button>
                                <div class="slideshow-indicators">
                                    <span class="slideshow-dot active" data-slide="0"></span>
                                    <span class="slideshow-dot" data-slide="1"></span>
                                    <span class="slideshow-dot" data-slide="2"></span>
                                    <span class="slideshow-dot" data-slide="3"></span>
                                    <span class="slideshow-dot" data-slide="4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="image-overlay"></div>
                    </div>
                    <div class="portfolio-details">
                        <h3>Tailored Applications</h3>
                        <p>Beyond our core products, we thrive on challenges. Whether it's a unique internal tool or a complex platform, we build custom applications that solve your specific problems.</p>
                        <ul>
                            <li><span class="list-icon">✓</span> Scalable Architectures</li>
                            <li><span class="list-icon">✓</span> Seamless Integrations</li>
                            <li><span class="list-icon">✓</span> User-Centric Design</li>
                            <li><span class="list-icon">✓</span> Ongoing Support</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="contact-section">
        <div class="container">
            <div class="contact-content section-animate">
                <div class="contact-header">
                    <h2 class="section-title">Get a Quote</h2>
                    <p class="section-subtitle">Let's discuss your project and turn your vision into reality.</p>
                </div>
                <div class="contact-form-wrapper">
                    @await Html.PartialAsync("_ContactFormPartial", Model)
                </div>
            </div>
        </div>
    </section>

</div>

@section Scripts {
    <script src="~/js/site.js" asp-append-version="true"></script>
}